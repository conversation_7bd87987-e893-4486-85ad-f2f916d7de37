"""
Template Extraction Module

Requirements:
Target: Extract all properties from example PPTX files for templating
Satisfied: Basic text and formatting extraction from slides
Pending: Shape extraction, image handling, chart data extraction
"""

from typing import Dict, List, Any, Optional
from pptx import Presentation
from pptx.shapes.base import BaseShape
from pptx.text.text import TextFrame
import json


def extract_text_from_shape(shape: BaseShape) -> Optional[Dict[str, Any]]:
    """H: Extract text and formatting from a shape"""
    if not hasattr(shape, 'text_frame') or not shape.has_text_frame:
        return None
    
    text_frame = shape.text_frame
    
    # W: Extract all text properties
    text_data = {
        "text": text_frame.text,
        "paragraphs": []
    }
    
    for paragraph in text_frame.paragraphs:
        para_data = {
            "text": paragraph.text,
            "level": paragraph.level,
            "alignment": str(paragraph.alignment) if paragraph.alignment else None,
            "runs": []
        }
        
        for run in paragraph.runs:
            run_data = {
                "text": run.text,
                "font_name": run.font.name,
                "font_size": run.font.size.pt if run.font.size else None,
                "bold": run.font.bold,
                "italic": run.font.italic,
                "color": str(run.font.color.rgb) if run.font.color and run.font.color.rgb else None
            }
            para_data["runs"].append(run_data)
        
        text_data["paragraphs"].append(para_data)
    
    return text_data


def extract_slide_properties(slide) -> Dict[str, Any]:
    """H: Extract all properties from a single slide"""
    slide_data = {
        "slide_layout": slide.slide_layout.name,
        "shapes": [],
        "placeholders": {},
        "background": None
    }
    
    # W: Extract background if exists
    if slide.background and slide.background.fill:
        fill = slide.background.fill
        if fill.type == 1:  # Solid fill
            slide_data["background"] = {
                "type": "solid",
                "color": str(fill.fore_color.rgb) if fill.fore_color.rgb else None
            }
    
    # W: Extract placeholders
    for placeholder in slide.placeholders:
        placeholder_data = {
            "idx": placeholder.placeholder_format.idx,
            "type": str(placeholder.placeholder_format.type),
            "text": placeholder.text if hasattr(placeholder, 'text') else None,
            "name": placeholder.name
        }
        
        # H: Extract text formatting if available
        text_data = extract_text_from_shape(placeholder)
        if text_data:
            placeholder_data["text_data"] = text_data
        
        slide_data["placeholders"][placeholder.placeholder_format.idx] = placeholder_data
    
    # W: Extract all shapes (non-placeholders)
    for shape in slide.shapes:
        if not shape.is_placeholder:
            shape_data = {
                "type": shape.shape_type,
                "name": shape.name,
                "left": shape.left,
                "top": shape.top,
                "width": shape.width,
                "height": shape.height
            }
            
            # H: Extract text if available
            text_data = extract_text_from_shape(shape)
            if text_data:
                shape_data["text_data"] = text_data
            
            slide_data["shapes"].append(shape_data)
    
    return slide_data


def extract_presentation_properties(pptx_path: str) -> Dict[str, Any]:
    """
    H: Extract all properties from a PowerPoint presentation
    
    Returns: Dictionary with all extractable properties
    """
    prs = Presentation(pptx_path)
    
    extracted = {
        "source_file": pptx_path,
        "slide_count": len(prs.slides),
        "slide_width": prs.slide_width,
        "slide_height": prs.slide_height,
        "slides": []
    }
    
    # W: Extract properties from each slide
    for idx, slide in enumerate(prs.slides):
        slide_data = extract_slide_properties(slide)
        slide_data["slide_index"] = idx
        extracted["slides"].append(slide_data)
    
    return extracted


def extract_text_content(pptx_path: str) -> List[Dict[str, str]]:
    """
    H: Extract just the text content from slides (simplified)
    
    Returns: List of dicts with slide index and text
    """
    prs = Presentation(pptx_path)
    text_content = []
    
    for idx, slide in enumerate(prs.slides):
        slide_text = {
            "slide_index": idx,
            "texts": []
        }
        
        # W: Extract all text from slide
        for shape in slide.shapes:
            if hasattr(shape, "text"):
                slide_text["texts"].append({
                    "shape_name": shape.name,
                    "text": shape.text
                })
        
        text_content.append(slide_text)
    
    return text_content


def save_extracted_properties(extracted: Dict[str, Any], output_path: str) -> str:
    """H: Save extracted properties to JSON file"""
    with open(output_path, 'w') as f:
        json.dump(extracted, f, indent=2, default=str)
    return output_path


if __name__ == "__main__":
    # W: Guard main for testing extraction
    print("Template Extractor Testing")
    print("=" * 30)
    
    # H: Test with a sample file (would need actual PPTX)
    test_pptx = "sample_template.pptx"
    
    try:
        # H: Full extraction
        print(f"\nExtracting from: {test_pptx}")
        properties = extract_presentation_properties(test_pptx)
        
        print(f"Slides found: {properties['slide_count']}")
        print(f"Slide dimensions: {properties['slide_width']} x {properties['slide_height']}")
        
        # H: Show first slide data
        if properties['slides']:
            first_slide = properties['slides'][0]
            print(f"\nFirst slide layout: {first_slide['slide_layout']}")
            print(f"Placeholders: {len(first_slide['placeholders'])}")
            print(f"Shapes: {len(first_slide['shapes'])}")
        
        # H: Save to JSON
        output_file = "extracted_properties.json"
        save_extracted_properties(properties, output_file)
        print(f"\nProperties saved to: {output_file}")
        
    except FileNotFoundError:
        print(f"\nNote: {test_pptx} not found. Create a sample PPTX to test extraction.")
        
        # H: Show sample extracted structure
        print("\nSample extracted structure:")
        sample = {
            "source_file": "example.pptx",
            "slides": [{
                "slide_index": 0,
                "placeholders": {
                    "0": {
                        "type": "Title",
                        "text": "Apollo Investment Analysis",
                        "text_data": {
                            "text": "Apollo Investment Analysis",
                            "paragraphs": [{
                                "text": "Apollo Investment Analysis",
                                "runs": [{
                                    "text": "Apollo Investment Analysis",
                                    "font_size": 36,
                                    "color": "FFFFFF"
                                }]
                            }]
                        }
                    }
                }
            }]
        }
        print(json.dumps(sample, indent=2))