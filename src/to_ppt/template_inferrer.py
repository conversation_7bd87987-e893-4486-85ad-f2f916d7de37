"""
Template Inference Module using LLM

Requirements:
Target: Use LLM to infer which extracted properties should be templatized
Satisfied: Basic inference logic to identify template variables
Pending: Advanced pattern recognition, multi-slide correlation
"""

from typing import Dict, List, Tuple
import re
from .template_models import (
    ExtractedProperties, ExtractedText, TemplateDefinition, 
    TemplateVariable
)


def identify_template_candidates(text: str) -> List[Tuple[str, str]]:
    """
    H: Identify potential template variables in text
    
    Returns: List of (variable_name, original_text) tuples
    """
    candidates = []
    
    # W: Pattern 1: Company names (capitalized words)
    company_pattern = r'\b[A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)*\b'
    for match in re.finditer(company_pattern, text):
        if match.group() not in ['The', 'This', 'That', 'These']:
            candidates.append(('company_name', match.group()))
    
    # W: Pattern 2: Dates (various formats)
    date_patterns = [
        r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}\b',
        r'\b\d{1,2}/\d{1,2}/\d{2,4}\b',
        r'\b\d{4}-\d{2}-\d{2}\b',
        r'\bQ[1-4]\s+\d{4}\b'
    ]
    for pattern in date_patterns:
        for match in re.finditer(pattern, text):
            candidates.append(('date', match.group()))
    
    # W: Pattern 3: Dollar amounts
    dollar_pattern = r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion|M|B))?'
    for match in re.finditer(dollar_pattern, text):
        candidates.append(('amount', match.group()))
    
    # W: Pattern 4: Percentages
    percent_pattern = r'\d+(?:\.\d+)?%'
    for match in re.finditer(percent_pattern, text):
        candidates.append(('percentage', match.group()))
    
    # W: Pattern 5: Confidentiality levels
    conf_pattern = r'(?:Strictly\s+)?Confidential'
    for match in re.finditer(conf_pattern, text, re.IGNORECASE):
        candidates.append(('confidentiality_level', match.group()))
    
    return candidates


def infer_template_variables(extracted: ExtractedProperties) -> List[TemplateVariable]:
    """
    H: Infer which text should become template variables
    
    This is a simplified version - in production would use actual LLM
    """
    variables = []
    seen_keys = set()
    
    for slide in extracted.slides:
        for text_item in slide.texts:
            if not text_item.text:
                continue
                
            # W: Find template candidates in this text
            candidates = identify_template_candidates(text_item.text)
            
            for var_type, original_text in candidates:
                # H: Create unique key for this variable
                key = var_type
                if key in seen_keys:
                    # W: Add number suffix for duplicates
                    i = 2
                    while f"{key}_{i}" in seen_keys:
                        i += 1
                    key = f"{key}_{i}"
                
                seen_keys.add(key)
                
                # H: Create template variable
                variable = TemplateVariable(
                    key=key,
                    original_text=original_text,
                    slide_index=slide.slide_index,
                    shape_name=text_item.shape_name,
                    description=f"Replace {var_type} in {text_item.shape_name}"
                )
                variables.append(variable)
    
    return variables


def create_template_dict(extracted: ExtractedProperties, variables: List[TemplateVariable]) -> Dict[str, str]:
    """
    H: Create minimal template dictionary from extracted properties
    
    Returns: Simple dict mapping keys to template strings
    """
    template_dict = {}
    
    # W: Process each slide's text
    for slide in extracted.slides:
        for text_item in slide.texts:
            if not text_item.text:
                continue
            
            # H: Start with original text
            template_text = text_item.text
            
            # W: Replace each variable occurrence with placeholder
            for var in variables:
                if (var.slide_index == slide.slide_index and 
                    var.shape_name == text_item.shape_name and
                    var.original_text in template_text):
                    template_text = template_text.replace(
                        var.original_text, 
                        f"{{{{{var.key}}}}}"
                    )
            
            # H: Only add to dict if template contains variables
            if "{{" in template_text:
                # W: Create key from slide and shape info
                dict_key = f"slide_{slide.slide_index}_{text_item.shape_name.lower().replace(' ', '_')}"
                template_dict[dict_key] = template_text
    
    return template_dict


def infer_template_from_extraction(extracted: ExtractedProperties) -> TemplateDefinition:
    """
    H: Main function to infer template from extracted properties
    
    In production, this would use an actual LLM for smarter inference
    """
    # W: Step 1: Identify template variables
    variables = infer_template_variables(extracted)
    
    # W: Step 2: Create template dictionary
    template_dict = create_template_dict(extracted, variables)
    
    # W: Step 3: Create template definition
    template_def = TemplateDefinition(
        template_dict=template_dict,
        variables=variables,
        source_file=extracted.source_file
    )
    
    return template_def


def simple_llm_inference_prompt(extracted_text: List[str]) -> str:
    """
    H: Generate prompt for LLM to infer template variables
    
    This shows what would be sent to an LLM in production
    """
    prompt = """Given the following text extracted from a PowerPoint presentation, 
identify which parts should become template variables.

Extracted texts:
"""
    for i, text in enumerate(extracted_text):
        prompt += f"\n{i+1}. {text}"
    
    prompt += """

Please identify:
1. Company names that should be replaced
2. Dates that should be parameterized  
3. Financial figures that might change
4. Any other text that looks like it should be a template variable

Return a simple dictionary mapping variable names to the text they should replace.
Example: {"company_name": "Apollo", "date": "June 2024"}
"""
    
    return prompt


if __name__ == "__main__":
    # W: Guard main for testing inference
    print("Template Inference Testing")
    print("=" * 30)
    
    # H: Create sample extracted data
    from .template_models import ExtractedSlide, ExtractedText
    
    sample_slide = ExtractedSlide(
        slide_index=0,
        slide_layout="Title Slide",
        texts=[
            ExtractedText(
                shape_name="Title",
                text="Apollo Investment Analysis",
                font_size=36
            ),
            ExtractedText(
                shape_name="Subtitle", 
                text="June 2024\nStrictly Confidential",
                font_size=24
            )
        ]
    )
    
    sample_extracted = ExtractedProperties(
        source_file="sample.pptx",
        slide_count=1,
        slides=[sample_slide]
    )
    
    # H: Test inference
    print("\nInferring template variables...")
    template_def = infer_template_from_extraction(sample_extracted)
    
    print(f"\nFound {len(template_def.variables)} template variables:")
    for var in template_def.variables:
        print(f"  - {var.key}: '{var.original_text}' on slide {var.slide_index}")
    
    print(f"\nTemplate dictionary:")
    for key, value in template_def.template_dict.items():
        print(f"  {key}: {value}")
    
    # H: Show LLM prompt example
    print("\n\nExample LLM prompt:")
    texts = [t.text for s in sample_extracted.slides for t in s.texts]
    print(simple_llm_inference_prompt(texts))