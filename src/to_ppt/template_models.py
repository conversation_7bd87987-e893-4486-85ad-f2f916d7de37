"""
Template Models for PPTX Template System

Requirements:
Target: Define models for template extraction, definition, and population
Satisfied: Basic models for template workflow
Pending: Advanced template features, conditional logic
"""

from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field


class ExtractedText(BaseModel):
    """W: Text content extracted from a shape/placeholder"""
    shape_name: str
    text: str
    font_size: Optional[float] = None
    font_name: Optional[str] = None
    color: Optional[str] = None
    bold: Optional[bool] = None
    placeholder_idx: Optional[int] = None


class ExtractedSlide(BaseModel):
    """W: Properties extracted from a single slide"""
    slide_index: int
    slide_layout: str
    texts: List[ExtractedText]
    background_color: Optional[str] = None
    placeholder_count: int = 0


class ExtractedProperties(BaseModel):
    """W: All properties extracted from a PPTX file"""
    source_file: str
    slide_count: int
    slides: List[ExtractedSlide]
    raw_data: Dict[str, Any] = Field(default_factory=dict, description="Full extraction data")


class TemplateVariable(BaseModel):
    """W: A single template variable definition"""
    key: str = Field(description="Variable name like 'company_name'")
    original_text: str = Field(description="Original text to be replaced")
    slide_index: int = Field(description="Which slide this appears on")
    shape_name: str = Field(description="Which shape contains this text")
    description: Optional[str] = Field(None, description="LLM-generated description")


class TemplateDefinition(BaseModel):
    """
    W: Minimal template dictionary with placeholders
    
    This is the simple dictionary format that maps variable names to template strings
    """
    template_dict: Dict[str, str] = Field(
        description="Simple dict like {'title': '{{company}} Analysis'}"
    )
    variables: List[TemplateVariable] = Field(
        description="Detailed info about each template variable"
    )
    source_file: str = Field(description="Original PPTX used as template")


class TemplateValues(BaseModel):
    """W: User-provided values to fill the template"""
    values: Dict[str, str] = Field(
        description="Values like {'company': 'Apollo', 'date': 'June 2024'}"
    )
    brief_text: Optional[str] = Field(None, description="Original brief for context")


class FilledTemplate(BaseModel):
    """W: Template with all variables filled"""
    filled_dict: Dict[str, str] = Field(
        description="Filled template like {'title': 'Apollo Analysis'}"
    )
    template_definition: TemplateDefinition
    values_used: TemplateValues


class TemplateContext(BaseModel):
    """W: Complete context for template-based generation"""
    extracted_properties: ExtractedProperties
    template_definition: TemplateDefinition
    filled_template: Optional[FilledTemplate] = None
    output_path: str = "generated_from_template.pptx"


# W: Simple template examples for testing
EXAMPLE_TEMPLATE_DICT = {
    "title": "{{company_name}} Investment Analysis",
    "subtitle": "{{date}}",
    "confidential": "{{confidentiality_level}} - {{company_name}}",
    "section_1_title": "{{target_company}} Overview",
    "section_1_content": "{{investment_thesis}}"
}

EXAMPLE_TEMPLATE_VALUES = {
    "company_name": "Apollo",
    "date": "June 2024",
    "confidentiality_level": "Strictly Confidential",
    "target_company": "HealthTech Co",
    "investment_thesis": "Strong growth potential in digital health market"
}


if __name__ == "__main__":
    # W: Guard main for testing models
    print("Template Models Testing")
    print("=" * 25)
    
    # H: Test template variable
    var = TemplateVariable(
        key="company_name",
        original_text="Apollo Investment Analysis",
        slide_index=0,
        shape_name="Title Placeholder",
        description="Name of the PE firm"
    )
    print(f"Variable: {var.key} replaces '{var.original_text}'")
    
    # H: Test template definition
    template_def = TemplateDefinition(
        template_dict=EXAMPLE_TEMPLATE_DICT,
        variables=[var],
        source_file="apollo_template.pptx"
    )
    print(f"\nTemplate has {len(template_def.variables)} variables")
    print(f"Template dict keys: {list(template_def.template_dict.keys())}")
    
    # H: Test template values
    values = TemplateValues(
        values=EXAMPLE_TEMPLATE_VALUES,
        brief_text="Apollo is considering an investment in HealthTech Co..."
    )
    
    # H: Test filled template
    filled_dict = {}
    for key, template_str in EXAMPLE_TEMPLATE_DICT.items():
        # W: Simple placeholder replacement
        filled_value = template_str
        for var_key, var_value in EXAMPLE_TEMPLATE_VALUES.items():
            filled_value = filled_value.replace(f"{{{{{var_key}}}}}", var_value)
        filled_dict[key] = filled_value
    
    filled = FilledTemplate(
        filled_dict=filled_dict,
        template_definition=template_def,
        values_used=values
    )
    
    print(f"\nFilled template:")
    for key, value in filled.filled_dict.items():
        print(f"  {key}: {value}")