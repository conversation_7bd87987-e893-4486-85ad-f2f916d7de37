"""
Apollo Private Equity Style Guide Module

Requirements:
Target: Provide Apollo PE brand colors, fonts, and layout rules for PPT generation
Satisfied: Brand colors, basic layout rules defined
Pending: Template integration, font specifications
"""

from typing import Dict, List, Tuple
from pydantic import BaseModel


class ApolloStyle(BaseModel):
    """Apollo PE brand style configuration"""
    
    # W: Apollo brand colors based on typical PE firm palettes
    primary_colors: Dict[str, str] = {
        "apollo_navy": "#008c76",      # W: Primary brand color
        "apollo_forest_green": "#395878",      # W: Secondary accent 
        "apollo_red": "#ea43353"
        "white": "#FFFFFF",
        "light_gray": "#F5F5F5",
        "dark_gray": "#4A4A4A",
        "chart_blue": "#4472C4",       # W: For data visualization
        "chart_green": "#70AD47",
        "chart_orange": "#FF8C00"
    }


    # W: Typography hierarchy for slides
    font_sizes: Dict[str, int] = {
        "title": 36,
        "subtitle": 24, 
        "body": 18,
        "caption": 14
    }
    
    # H: Slide layout specifications
    margins: Dict[str, float] = {
        "top": 1.0,
        "bottom": 1.0, 
        "left": 1.0,
        "right": 1.0
    }


def get_apollo_colors() -> Dict[str, str]:
    """H: Return Apollo brand color palette"""
    return ApolloStyle().primary_colors


def get_title_slide_config() -> Dict:
    """H: Configuration for Apollo title slides"""
    return {
        "background_color": ApolloStyle().primary_colors["apollo_navy"],
        "title_color": ApolloStyle().primary_colors["white"],
        "subtitle_color": ApolloStyle().primary_colors["apollo_gold"],
        "title_size": ApolloStyle().font_sizes["title"],
        "subtitle_size": ApolloStyle().font_sizes["subtitle"]
    }


def get_content_slide_config() -> Dict:
    """H: Configuration for Apollo content slides"""
    return {
        "background_color": ApolloStyle().primary_colors["white"],
        "title_color": ApolloStyle().primary_colors["apollo_navy"],
        "body_color": ApolloStyle().primary_colors["dark_gray"],
        "accent_color": ApolloStyle().primary_colors["apollo_gold"]
    }


if __name__ == "__main__":
    # W: Guard main for manual testing with colors
    print("\033[1;37mApollo PE Style Guide\033[0m")
    print("=" * 30)
    
    style = ApolloStyle()
    
    # H: Display colors with ANSI codes
    print("\nPrimary Colors:")
    for name, hex_code in style.primary_colors.items():
        rgb = tuple(int(hex_code.lstrip('#')[i:i+2], 16) for i in (0, 2, 4))
        print(f"  \033[38;2;{rgb[0]};{rgb[1]};{rgb[2]}m{name}\033[0m: {hex_code}")
    
    print(f"\nFont Sizes: {style.font_sizes}")
    
    print("\n\033[48;2;27;41;81;38;2;255;255;255mTitle Slide Config:\033[0m")
    title_config = get_title_slide_config()
    for key, value in title_config.items():
        print(f"  {key}: {value}")
    
    print("\n\033[38;2;27;41;81mContent Slide Config:\033[0m")
    content_config = get_content_slide_config()
    for key, value in content_config.items():
        print(f"  {key}: {value}")