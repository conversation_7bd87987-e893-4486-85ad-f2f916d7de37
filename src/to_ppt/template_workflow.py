"""
End-to-End Template Workflow

Requirements:
Target: Complete workflow from template PPTX to filled output
Satisfied: Basic integration of all template modules
Pending: Error handling, validation, advanced features
"""

from typing import Optional
from pathlib import Path

from .template_extractor import extract_presentation_properties, extract_text_content
from .template_inferrer import infer_template_from_extraction
from .template_populator import populate_template_from_brief, fill_template
from .pptx_rebuilder import rebuild_pptx_from_template, simple_text_replacement
from .template_models import ExtractedProperties, ExtractedText, ExtractedSlide


def create_template_from_pptx(template_path: str) -> dict:
    """
    H: Step 1 - Extract and create template from example PPTX
    
    Returns: Template definition as simple dict
    """
    print(f"Extracting properties from: {template_path}")
    
    # W: Extract all properties
    raw_properties = extract_presentation_properties(template_path)
    
    # H: Convert to our model format
    slides = []
    for slide_data in raw_properties['slides']:
        texts = []
        
        # W: Extract text from placeholders
        for idx, placeholder in slide_data['placeholders'].items():
            if placeholder.get('text'):
                texts.append(ExtractedText(
                    shape_name=placeholder['name'],
                    text=placeholder['text'],
                    placeholder_idx=int(idx)
                ))
        
        # W: Extract text from shapes
        for shape in slide_data['shapes']:
            if shape.get('text_data'):
                texts.append(ExtractedText(
                    shape_name=shape['name'],
                    text=shape['text_data']['text']
                ))
        
        slides.append(ExtractedSlide(
            slide_index=slide_data['slide_index'],
            slide_layout=slide_data['slide_layout'],
            texts=texts
        ))
    
    extracted = ExtractedProperties(
        source_file=template_path,
        slide_count=raw_properties['slide_count'],
        slides=slides,
        raw_data=raw_properties
    )
    
    # W: Infer template variables
    print("Inferring template variables...")
    template_def = infer_template_from_extraction(extracted)
    
    print(f"Created template with {len(template_def.variables)} variables")
    
    # H: Return simple dict format
    return {
        "template_dict": template_def.template_dict,
        "variables": [
            {
                "key": var.key,
                "original": var.original_text,
                "slide": var.slide_index,
                "shape": var.shape_name
            }
            for var in template_def.variables
        ],
        "source_file": template_def.source_file
    }


def generate_from_template(
    template_pptx: str,
    brief_text: str,
    output_path: str
) -> str:
    """
    H: Complete workflow: template PPTX + brief -> filled PPTX
    
    Returns: Path to generated file
    """
    print("\n=== Template-Based PPT Generation ===")
    
    # W: Step 1: Extract template
    print("\n1. Extracting from template...")
    raw_properties = extract_presentation_properties(template_pptx)
    
    # H: Convert to model format
    slides = []
    for slide_data in raw_properties['slides']:
        texts = []
        for idx, placeholder in slide_data['placeholders'].items():
            if placeholder.get('text'):
                texts.append(ExtractedText(
                    shape_name=placeholder['name'],
                    text=placeholder['text'],
                    placeholder_idx=int(idx)
                ))
        slides.append(ExtractedSlide(
            slide_index=slide_data['slide_index'],
            slide_layout=slide_data['slide_layout'],
            texts=texts
        ))
    
    extracted = ExtractedProperties(
        source_file=template_pptx,
        slide_count=raw_properties['slide_count'],
        slides=slides
    )
    
    # W: Step 2: Infer template
    print("\n2. Inferring template variables...")
    template_def = infer_template_from_extraction(extracted)
    print(f"   Found {len(template_def.variables)} variables")
    
    # W: Step 3: Populate template
    print("\n3. Extracting values from brief...")
    template_values = populate_template_from_brief(template_def, brief_text)
    print(f"   Extracted {len(template_values.values)} values")
    
    # W: Step 4: Fill template
    print("\n4. Filling template...")
    filled_template = fill_template(template_def, template_values)
    
    # W: Step 5: Rebuild PPTX
    print("\n5. Rebuilding PowerPoint...")
    result_path = rebuild_pptx_from_template(
        template_pptx,
        filled_template,
        output_path
    )
    
    print(f"\n✅ Generated: {result_path}")
    return result_path


def quick_template_test(
    template_pptx: str,
    replacements: dict,
    output_path: str
) -> str:
    """
    H: Quick test with manual replacements
    
    Args:
        template_pptx: Template file path
        replacements: Dict like {"{{company}}": "Apollo"}
        output_path: Output file path
    """
    print(f"Quick template test: {len(replacements)} replacements")
    
    result = simple_text_replacement(
        template_pptx,
        replacements,
        output_path
    )
    
    print(f"✅ Created: {result}")
    return result


if __name__ == "__main__":
    # W: Guard main for workflow testing
    print("Template Workflow Testing")
    print("=" * 30)
    
    # H: Example workflow
    print("\nExample workflow:")
    print("1. Extract properties from template.pptx")
    print("2. Infer template variables (company_name, date, etc)")
    print("3. Extract values from user brief")
    print("4. Fill template with values")
    print("5. Generate final presentation")
    
    # H: Sample template creation
    print("\n\nSample template structure:")
    sample_template = {
        "template_dict": {
            "slide_0_title": "{{company_name}} Investment Analysis",
            "slide_0_subtitle": "{{date}}\n{{confidentiality_level}}",
            "slide_1_title": "{{target_company}} Overview"
        },
        "variables": [
            {"key": "company_name", "original": "Apollo", "slide": 0},
            {"key": "date", "original": "June 2024", "slide": 0},
            {"key": "target_company", "original": "Target Co", "slide": 1}
        ]
    }
    
    import json
    print(json.dumps(sample_template, indent=2))
    
    # H: Sample brief
    print("\n\nSample brief:")
    sample_brief = """
    BlackRock is evaluating an investment in TechStart Inc, 
    a promising AI company. The opportunity represents 
    significant growth potential in the enterprise AI market.
    December 2024 - Strictly Confidential
    """
    print(sample_brief)
    
    print("\n\nNote: To test the full workflow, create a template PPTX with")
    print("placeholders like {{company_name}}, {{date}}, etc.")