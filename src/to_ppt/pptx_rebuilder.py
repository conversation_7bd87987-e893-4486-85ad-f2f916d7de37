"""
PPTX Rebuilder Module

Requirements:
Target: Replace template placeholders in PPTX with filled values
Satisfied: Basic text replacement while preserving formatting
Pending: Complex shape handling, chart updates, image replacement
"""

from typing import Dict, Optional
from pptx import Presentation
from pptx.text.text import TextFrame
from .template_models import FilledTemplate, TemplateVariable


def replace_text_in_shape(shape, old_text: str, new_text: str) -> bool:
    """
    H: Replace text in a shape while preserving formatting
    
    Returns: True if replacement was made
    """
    if not hasattr(shape, 'text_frame') or not shape.has_text_frame:
        return False
    
    text_frame = shape.text_frame
    replaced = False
    
    # W: Process each paragraph
    for paragraph in text_frame.paragraphs:
        # H: Check if old_text exists in paragraph
        if old_text in paragraph.text:
            # W: Store original formatting from first run
            if paragraph.runs:
                first_run = paragraph.runs[0]
                font_name = first_run.font.name
                font_size = first_run.font.size
                font_bold = first_run.font.bold
                font_italic = first_run.font.italic
                font_color = first_run.font.color.rgb if first_run.font.color else None
            else:
                font_name = font_size = font_bold = font_italic = font_color = None
            
            # H: Replace text in paragraph
            new_paragraph_text = paragraph.text.replace(old_text, new_text)
            
            # W: Clear runs and create new one with formatting
            paragraph.clear()
            run = paragraph.add_run()
            run.text = new_paragraph_text
            
            # H: Restore formatting
            if font_name:
                run.font.name = font_name
            if font_size:
                run.font.size = font_size
            if font_bold is not None:
                run.font.bold = font_bold
            if font_italic is not None:
                run.font.italic = font_italic
            if font_color:
                run.font.color.rgb = font_color
            
            replaced = True
    
    return replaced


def rebuild_pptx_from_template(
    template_path: str,
    filled_template: FilledTemplate,
    output_path: str
) -> str:
    """
    H: Main function to rebuild PPTX with filled template values
    
    Returns: Path to generated PPTX file
    """
    # W: Load the template presentation
    prs = Presentation(template_path)
    
    # H: Create mapping of what to replace
    replacements = {}
    for var in filled_template.template_definition.variables:
        placeholder = f"{{{{{var.key}}}}}"
        if var.key in filled_template.values_used.values:
            replacements[placeholder] = filled_template.values_used.values[var.key]
    
    # W: Also handle direct template dict replacements
    for key, template_str in filled_template.template_definition.template_dict.items():
        if key in filled_template.filled_dict:
            # H: Extract all placeholders from template string
            import re
            placeholders = re.findall(r'\{\{(\w+)\}\}', template_str)
            for placeholder_key in placeholders:
                if placeholder_key in filled_template.values_used.values:
                    full_placeholder = f"{{{{{placeholder_key}}}}}"
                    replacements[full_placeholder] = filled_template.values_used.values[placeholder_key]
    
    # W: Process each slide
    replacement_count = 0
    for slide_idx, slide in enumerate(prs.slides):
        # H: Process all shapes in slide
        for shape in slide.shapes:
            # W: Try to replace text in shape
            for old_text, new_text in replacements.items():
                if hasattr(shape, 'text') and old_text in shape.text:
                    if replace_text_in_shape(shape, old_text, new_text):
                        replacement_count += 1
    
    # W: Save the modified presentation
    prs.save(output_path)
    
    print(f"Rebuilt PPTX with {replacement_count} replacements")
    return output_path


def simple_text_replacement(
    template_path: str,
    replacements: Dict[str, str],
    output_path: str
) -> str:
    """
    H: Simple version for direct text replacement
    
    Args:
        template_path: Source PPTX file
        replacements: Dict of text to find -> text to replace with
        output_path: Where to save result
    """
    prs = Presentation(template_path)
    
    for slide in prs.slides:
        for shape in slide.shapes:
            for old_text, new_text in replacements.items():
                if hasattr(shape, 'text') and old_text in shape.text:
                    replace_text_in_shape(shape, old_text, new_text)
    
    prs.save(output_path)
    return output_path


if __name__ == "__main__":
    # W: Guard main for testing rebuilder
    print("PPTX Rebuilder Testing")
    print("=" * 25)
    
    # H: Test with sample data
    from .template_models import (
        TemplateDefinition, TemplateValues, FilledTemplate,
        TemplateVariable, EXAMPLE_TEMPLATE_DICT
    )
    
    # W: Create sample filled template
    sample_vars = [
        TemplateVariable(
            key="company_name",
            original_text="{{company_name}}",
            slide_index=0,
            shape_name="Title"
        )
    ]
    
    template_def = TemplateDefinition(
        template_dict={"title": "{{company_name}} Analysis"},
        variables=sample_vars,
        source_file="template.pptx"
    )
    
    values = TemplateValues(
        values={"company_name": "Apollo"},
        brief_text="Apollo investment brief..."
    )
    
    filled = FilledTemplate(
        filled_dict={"title": "Apollo Analysis"},
        template_definition=template_def,
        values_used=values
    )
    
    print("Sample filled template:")
    print(f"  Original: {{{{company_name}}}} Analysis")
    print(f"  Filled: Apollo Analysis")
    
    # H: Test simple replacement
    print("\nSimple replacement example:")
    replacements = {
        "{{company_name}}": "Apollo",
        "{{date}}": "December 2024",
        "{{confidentiality}}": "Strictly Confidential"
    }
    
    print("Replacements:")
    for old, new in replacements.items():
        print(f"  {old} -> {new}")
    
    print("\nNote: Actual testing requires a template PPTX file")