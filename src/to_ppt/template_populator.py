"""
Template Populator Module

Requirements:
Target: Use LLM to extract values from brief and populate template
Satisfied: Basic value extraction and template filling
Pending: Complex value inference, validation, multi-value handling
"""

from typing import Dict, Optional, List
import re
from datetime import datetime
from .template_models import (
    TemplateDefinition, TemplateValues, FilledTemplate,
    TemplateVariable
)


def extract_value_for_variable(brief_text: str, variable: TemplateVariable) -> Optional[str]:
    """
    H: Extract value for a specific template variable from brief
    
    This is simplified - in production would use LLM
    """
    # W: Handle different variable types
    if variable.key.startswith('company_name'):
        # H: Look for capitalized company names
        companies = re.findall(r'\b[A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)*(?:\s+(?:Co|Corp|Inc|LLC|LP|Partners))?\b', brief_text)
        if companies:
            return companies[0]
        return "Apollo"  # Default
    
    elif variable.key.startswith('date'):
        # H: Look for dates or use current
        date_matches = re.findall(r'\b(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{4}\b', brief_text)
        if date_matches:
            return date_matches[0]
        # W: Default to current month/year
        return datetime.now().strftime("%B %Y")
    
    elif variable.key.startswith('amount'):
        # H: Extract dollar amounts
        amounts = re.findall(r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion|M|B))?', brief_text)
        if amounts:
            return amounts[0]
        return "$100M"  # Default
    
    elif variable.key.startswith('percentage'):
        # H: Extract percentages
        percentages = re.findall(r'\d+(?:\.\d+)?%', brief_text)
        if percentages:
            return percentages[0]
        return "25%"  # Default
    
    elif variable.key == 'confidentiality_level':
        # H: Check for confidentiality mentions
        if 'strictly confidential' in brief_text.lower():
            return "Strictly Confidential"
        return "Confidential"
    
    elif variable.key == 'target_company':
        # H: Look for "investment in X" or "acquire X" patterns
        target_patterns = [
            r'investment in ([A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)*)',
            r'acquire ([A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)*)',
            r'target company[,\s]+([A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)*)'
        ]
        for pattern in target_patterns:
            match = re.search(pattern, brief_text, re.IGNORECASE)
            if match:
                return match.group(1)
        return "Target Company"  # Default
    
    elif variable.key == 'investment_thesis':
        # H: Extract key investment points
        thesis_patterns = [
            r'investment thesis[:\s]+([^.]+\.)',
            r'opportunity[:\s]+([^.]+\.)',
            r'value creation[:\s]+([^.]+\.)'
        ]
        for pattern in thesis_patterns:
            match = re.search(pattern, brief_text, re.IGNORECASE)
            if match:
                return match.group(1).strip()
        return "Strong growth potential and market position"  # Default
    
    # W: Generic fallback
    return f"[{variable.key}]"


def populate_template_from_brief(
    template_def: TemplateDefinition, 
    brief_text: str
) -> TemplateValues:
    """
    H: Extract values from brief to fill template
    
    In production, this would use LLM for better extraction
    """
    values = {}
    
    # W: Extract value for each template variable
    for variable in template_def.variables:
        extracted_value = extract_value_for_variable(brief_text, variable)
        if extracted_value:
            values[variable.key] = extracted_value
    
    return TemplateValues(
        values=values,
        brief_text=brief_text
    )


def fill_template(
    template_def: TemplateDefinition,
    template_values: TemplateValues
) -> FilledTemplate:
    """
    H: Fill template dictionary with provided values
    """
    filled_dict = {}
    
    # W: Process each template string
    for key, template_str in template_def.template_dict.items():
        filled_str = template_str
        
        # H: Replace each variable placeholder
        for var_key, var_value in template_values.values.items():
            placeholder = f"{{{{{var_key}}}}}"
            filled_str = filled_str.replace(placeholder, var_value)
        
        filled_dict[key] = filled_str
    
    return FilledTemplate(
        filled_dict=filled_dict,
        template_definition=template_def,
        values_used=template_values
    )


def generate_llm_prompt_for_population(
    template_def: TemplateDefinition,
    brief_text: str
) -> str:
    """
    H: Generate prompt for LLM to populate template
    
    This shows what would be sent to an LLM in production
    """
    prompt = f"""Given the following brief and template variables, extract the appropriate values.

Brief:
{brief_text}

Template variables to fill:
"""
    
    for var in template_def.variables:
        prompt += f"\n- {var.key}: {var.description or 'Extract appropriate value'}"
    
    prompt += """

Please return a dictionary with values for each variable.
Example: {"company_name": "Apollo", "date": "June 2024", "target_company": "HealthTech Inc"}

Make sure to:
1. Extract actual values from the brief when available
2. Use sensible defaults when values aren't explicitly mentioned
3. Maintain consistency across related variables
"""
    
    return prompt


if __name__ == "__main__":
    # W: Guard main for testing population
    print("Template Populator Testing")
    print("=" * 30)
    
    # H: Create sample template definition
    from .template_models import EXAMPLE_TEMPLATE_DICT
    
    sample_variables = [
        TemplateVariable(
            key="company_name",
            original_text="Apollo",
            slide_index=0,
            shape_name="Title"
        ),
        TemplateVariable(
            key="date",
            original_text="June 2024",
            slide_index=0,
            shape_name="Subtitle"
        ),
        TemplateVariable(
            key="target_company",
            original_text="Target Company",
            slide_index=1,
            shape_name="Title"
        )
    ]
    
    template_def = TemplateDefinition(
        template_dict=EXAMPLE_TEMPLATE_DICT,
        variables=sample_variables,
        source_file="template.pptx"
    )
    
    # H: Test with sample brief
    sample_brief = """
    Apollo is evaluating an investment opportunity in HealthTech Solutions, 
    a leading digital health platform. The company has shown 45% annual growth 
    and represents a compelling opportunity in the healthcare technology sector.
    This document is strictly confidential.
    """
    
    # H: Populate template
    print("\nExtracting values from brief...")
    values = populate_template_from_brief(template_def, sample_brief)
    
    print("\nExtracted values:")
    for key, value in values.values.items():
        print(f"  {key}: {value}")
    
    # H: Fill template
    filled = fill_template(template_def, values)
    
    print("\nFilled template:")
    for key, value in filled.filled_dict.items():
        if "{{" not in value:  # Only show fully filled entries
            print(f"  {key}: {value}")
    
    # H: Show LLM prompt
    print("\n\nExample LLM prompt:")
    print(generate_llm_prompt_for_population(template_def, sample_brief))