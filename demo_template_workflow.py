#!/usr/bin/env python3
"""
Demo script for template-based PowerPoint generation

Requirements:
Target: Demonstrate the complete template workflow
Satisfied: End-to-end demo with example data
Pending: Real PPTX file handling
"""

import json
from pathlib import Path

# Add src to path
import sys
sys.path.append('src')

from to_ppt.template_workflow import create_template_from_pptx, generate_from_template
from to_ppt.pptx_rebuilder import simple_text_replacement
from to_ppt.brief_to_ppt import generate_ppt_from_deck_spec, brief_to_deck_spec


def create_sample_template_pptx():
    """H: Create a sample template PPTX for testing"""
    print("Creating sample template PPTX...")
    
    # W: Use existing functionality to create a template
    sample_brief = """{{company_name}} Investment Opportunity

{{target_company}} Analysis
{{target_company}} operates in the {{industry}} sector with strong fundamentals.

Financial Overview
Revenue: {{revenue}} with {{growth_rate}} annual growth. 

Investment Date: {{date}}
{{confidentiality_level}}"""
    
    deck = brief_to_deck_spec(sample_brief, "{{company_name}} Investment Analysis")
    deck.company_name = "{{company_name}}"
    deck.confidentiality_level = "{{confidentiality_level}}"
    deck.output_path = "template_example.pptx"
    
    generate_ppt_from_deck_spec(deck)
    print(f"✅ Created template: {deck.output_path}")
    
    return deck.output_path


def demo_template_extraction():
    """H: Demo Step 1 - Extract template from PPTX"""
    print("\n=== STEP 1: Template Extraction ===")
    
    # W: Create template if doesn't exist
    template_path = "template_example.pptx"
    if not Path(template_path).exists():
        template_path = create_sample_template_pptx()
    
    # H: Extract template
    try:
        template_data = create_template_from_pptx(template_path)
        
        print("\nExtracted template structure:")
        print(json.dumps(template_data, indent=2))
        
        return template_data
    except Exception as e:
        print(f"Note: {e}")
        print("Returning mock template data for demo...")
        
        # W: Mock template for demo
        return {
            "template_dict": {
                "slide_0_title": "{{company_name}} Investment Analysis",
                "slide_0_subtitle": "{{date}}\n{{confidentiality_level}}",
                "slide_1_title": "{{target_company}} Analysis",
                "slide_1_content": "{{target_company}} operates in the {{industry}} sector",
                "slide_2_content": "Revenue: {{revenue}} with {{growth_rate}} annual growth"
            },
            "variables": [
                {"key": "company_name", "original": "Apollo", "slide": 0, "shape": "Title"},
                {"key": "date", "original": "June 2024", "slide": 0, "shape": "Subtitle"},
                {"key": "target_company", "original": "TechCo", "slide": 1, "shape": "Title"},
                {"key": "industry", "original": "technology", "slide": 1, "shape": "Content"},
                {"key": "revenue", "original": "$100M", "slide": 2, "shape": "Content"},
                {"key": "growth_rate", "original": "25%", "slide": 2, "shape": "Content"}
            ],
            "source_file": template_path
        }


def demo_template_population():
    """H: Demo Step 2 - Populate template from brief"""
    print("\n=== STEP 2: Template Population ===")
    
    # W: Sample investment brief
    brief = """
    BlackRock is considering a major investment in CloudAI Systems, 
    a leading artificial intelligence company in the enterprise software sector.
    
    CloudAI has achieved $450M in revenue with an impressive 85% annual growth rate.
    The company's AI platform serves Fortune 500 enterprises.
    
    This investment opportunity is being evaluated for Q1 2025.
    All materials are strictly confidential.
    """
    
    print("Investment Brief:")
    print("-" * 40)
    print(brief)
    print("-" * 40)
    
    # H: Values that would be extracted
    extracted_values = {
        "company_name": "BlackRock",
        "target_company": "CloudAI Systems",
        "industry": "artificial intelligence",
        "revenue": "$450M",
        "growth_rate": "85%",
        "date": "Q1 2025",
        "confidentiality_level": "Strictly Confidential"
    }
    
    print("\nExtracted values:")
    for key, value in extracted_values.items():
        print(f"  {key}: {value}")
    
    return brief, extracted_values


def demo_full_workflow():
    """H: Demo complete workflow"""
    print("\n=== FULL WORKFLOW DEMO ===")
    
    # W: Get template and brief
    template_data = demo_template_extraction()
    brief, values = demo_template_population()
    
    print("\n=== STEP 3: Template Filling ===")
    
    # H: Show before/after
    print("\nTemplate → Filled:")
    print("-" * 50)
    
    for key, template_str in template_data["template_dict"].items():
        filled_str = template_str
        for var_key, var_value in values.items():
            filled_str = filled_str.replace(f"{{{{{var_key}}}}}", var_value)
        
        if template_str != filled_str:  # Only show changed items
            print(f"\n{key}:")
            print(f"  Before: {template_str}")
            print(f"  After:  {filled_str}")
    
    print("\n=== STEP 4: Generate Final PPTX ===")
    
    # H: Try actual generation
    template_path = template_data["source_file"]
    output_path = "demo_output_filled.pptx"
    
    if Path(template_path).exists():
        try:
            # W: Use simple replacement for demo
            replacements = {f"{{{{{k}}}}}": v for k, v in values.items()}
            
            result = simple_text_replacement(
                template_path,
                replacements,
                output_path
            )
            
            print(f"\n✅ Success! Generated: {result}")
            
        except Exception as e:
            print(f"\n⚠️  Generation skipped: {e}")
    else:
        print(f"\n⚠️  Template file not found: {template_path}")
        print("   Create a PPTX with placeholders like {{company_name}} to test")
    
    # H: Summary
    print("\n=== WORKFLOW SUMMARY ===")
    print("1. ✅ Extracted template with variables")
    print("2. ✅ Populated values from investment brief")  
    print("3. ✅ Filled template dictionary")
    print("4. 🔄 Generated final PPTX (requires template file)")


if __name__ == "__main__":
    print("🚀 Template-Based PowerPoint Generation Demo")
    print("=" * 50)
    
    demo_full_workflow()
    
    print("\n\n💡 Next Steps:")
    print("1. Create a real PPTX with {{placeholders}}")
    print("2. Run: python demo_template_workflow.py")
    print("3. Check generated 'demo_output_filled.pptx'")
    
    print("\n📝 To use in production:")
    print("from src.to_ppt.template_workflow import generate_from_template")
    print('generate_from_template("template.pptx", "Investment brief...", "output.pptx")')