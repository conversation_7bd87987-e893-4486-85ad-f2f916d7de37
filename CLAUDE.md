# TO_PPT - Brief to PowerPoint Converter

## Mini-Spec (<PERSON><PERSON><PERSON><PERSON> from User)
Task: make a ppt slide based on a brief
- stick to the style guide.
- LATER - only use the templates provided.

1. make a temporary faux style guide. Include a set of colours and very simple rules for applying colouring and schemes to ppt slides at private equity fund Apollo.
1.2 same for charts etc within ppt slides.

**Updated Goal**: Take an example PowerPoint as a template, extract its properties, use LLM to infer template variables, then populate the template with values from a brief.

## Project Structure
```
to_ppt/
├── src/to_ppt/
│   ├── __init__.py
│   ├── apollo_style.py         # Apollo PE brand colors and style rules
│   ├── models.py               # Pydantic models for PPT generation
│   ├── brief_to_ppt.py         # Main conversion logic (original approach)
│   ├── template_extractor.py   # Extract properties from example PPTX
│   ├── template_models.py      # Models for template workflow
│   ├── template_inferrer.py    # Infer template variables using patterns/LLM
│   ├── template_populator.py   # Extract values from brief to fill template
│   ├── pptx_rebuilder.py       # Rebuild PPTX with filled template values
│   └── template_workflow.py    # End-to-end template-based generation
├── tests/
│   ├── __init__.py
│   ├── test_apollo_style.py
│   ├── test_models.py
│   └── test_brief_to_ppt.py
├── scripts/
│   └── __init__.py
├── .venv/                   # Virtual environment
├── .claude/                 # Claude-specific files
├── run.py                   # Main CLI entry point
├── justfile                 # Command shortcuts
├── pyproject.toml          # Package configuration
├── .env                    # Environment variables
└── README_SPEC             # User specifications
```

## Requirements (Expanded)

### Core Functionality
- [x] Convert text briefs to PowerPoint presentations
- [x] Apply Apollo PE brand styling (navy #1B2951, gold #C5A572)
- [x] Support basic slide types (title, content, bullet points)
- [x] CLI interface for file input/output
- [x] Guard main blocks in all scripts for manual testing
- [x] Pydantic models for type safety and validation

### Technical Implementation
- [x] Use python-pptx library for PowerPoint generation
- [x] Implement Apollo PE style guide with colors and typography
- [x] Brief parsing (basic paragraph splitting)
- [x] Configurable output paths and titles
- [x] Environment variable configuration
- [x] Proper module structure with __init__.py files

### Testing & Quality
- [x] Regression tests for all core functionality
- [x] Test cases with descriptive "if X then broken" format
- [x] Guard main demonstrations in each module
- [x] Docstrings with requirements tracking (target vs satisfied)

### Future Enhancements (Pending)
- [ ] LLM integration for intelligent brief parsing
- [ ] Template system from existing PPTX files
- [ ] Advanced chart generation for PE presentations
- [ ] Batch processing capabilities
- [ ] JSON intermediary format for template parametrization

## Usage Examples

### CLI Usage
```bash
# Basic usage
python run.py "Investment brief text here"

# File input
python run.py sample_brief.txt -o apollo_investment.pptx

# With company name
python run.py brief.txt --company "Target Co." --title "Apollo Analysis"
```

### Justfile Commands
```bash
just setup      # Install dependencies
just run        # Run with sample brief
just test       # Run all tests
just clean      # Clean generated files
```

### Module Testing
```bash
python src/to_ppt/apollo_style.py    # Test style guide
python src/to_ppt/models.py          # Test Pydantic models
python src/to_ppt/brief_to_ppt.py    # Test conversion
```

## Design Decisions

### Apollo PE Styling
- Primary: Navy (#1B2951) for titles and backgrounds
- Accent: Gold (#C5A572) for highlights and subtitles
- Typography: 36pt titles, 18pt body text
- Confidentiality levels: "Confidential" (default), "Strictly Confidential"

### Code Architecture
- Minimal, brittle code following user conventions (no error handling)
- FCIS (Fast, Concise, Idiomatic, Specific) approach
- DRY principles with explanatory comments (W:What, H:How)
- Pydantic models for future-proof data validation

### File Organization
- Models in separate module for reusability
- Style guide as independent module
- Main conversion logic with clear separation of concerns
- Tests mirror source structure with descriptive assertions

## Template-Based Workflow

### How it works:
1. **Extract**: Read example PPTX → extract all text and properties
2. **Infer**: Identify template variables (company names, dates, amounts)
3. **Template**: Create minimal dict with placeholders `{"title": "{{company}} Analysis"}`
4. **Populate**: Extract values from brief to fill template
5. **Rebuild**: Replace placeholders in original PPTX, preserving formatting

### Template Dictionary Format:
```python
{
    "slide_0_title": "{{company_name}} Investment Analysis",
    "slide_0_subtitle": "{{date}}\n{{confidentiality_level}}",
    "slide_1_title": "{{target_company}} Overview"
}
```

### Usage:
```python
from src.to_ppt.template_workflow import generate_from_template

# Full workflow
output = generate_from_template(
    template_pptx="apollo_template.pptx",
    brief_text="Apollo evaluating investment in TechCo...",
    output_path="filled_presentation.pptx"
)

# Quick test with manual replacements
from src.to_ppt.template_workflow import quick_template_test
quick_template_test(
    "template.pptx",
    {"{{company}}": "Apollo", "{{date}}": "Dec 2024"},
    "output.pptx"
)
```

## Current Status
- ✅ Original brief-to-PPT functionality (creates from scratch)
- ✅ Template extraction from example PPTX files
- ✅ Template variable inference (pattern-based, LLM-ready)
- ✅ Template population from briefs
- ✅ PPTX rebuilding with format preservation
- 🚧 Actual LLM integration (currently using patterns)
- 🚧 Complex shape/chart handling