[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "to_ppt"
version = "0.1.0"
description = "Convert briefs to Apollo PE styled PowerPoint presentations"
requires-python = ">=3.8"
dependencies = [
    "python-pptx>=0.6.21",
    "pydantic>=2.0.0",
    "rich>=14.0.0",
    "pyyaml>=6.0.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
]

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.ruff]
line-length = 100
target-version = "py38"

[tool.black]
line-length = 100
target-version = ["py38"]
