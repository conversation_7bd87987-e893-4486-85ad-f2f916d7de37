# pypptx_q.py

from pydantic import BaseModel
from typing import List
import yaml

class Slide(BaseModel):
    title: str
    text: str
    layout: int

# Option 2: Multi-line string with YAML content
yaml_content = """
slides:
  - title: first title
    text: some text
    layout: 0
  - title: second title
    text: more text
    layout: 1
  - title: third slide
    text: with the previous layout
    layout: 1
"""

slides_data = yaml.safe_load(yaml_content)

# Option 3: Python data structures directly
slides: List[Slide] = [
    Slide(title="first title", text="some text", layout=0),
    Slide(title="second title", text="more text", layout=1),
    Slide(title="third slide", text="with the previous layout", layout=1)
]


