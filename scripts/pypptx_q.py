# pypptx_q.py

from pydantic import BaseModel
from typing import List
import yaml
from pathlib import Path
from rich import print as rprint
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from pptx import Presentation

# Ensure .tmp directory exists
TMP_DIR = Path(".tmp")
TMP_DIR.mkdir(exist_ok=True)

console = Console()

class Slide(BaseModel):
    title: str
    text: str
    layout: int

class PPTSlideDisplay:
    """Helper class for displaying and saving PPT slides"""

    @staticmethod
    def create_ppt_from_slides(slides: List[Slide], title: str = "Generated Presentation") -> Presentation:
        """Create a PowerPoint presentation from slide data"""
        prs = Presentation()

        # Create title slide
        title_slide_layout = prs.slide_layouts[0]
        title_slide = prs.slides.add_slide(title_slide_layout)
        title_slide.shapes.title.text = title

        # Create content slides
        for slide_data in slides:
            slide_layout = prs.slide_layouts[slide_data.layout]
            slide = prs.slides.add_slide(slide_layout)

            if slide.shapes.title:
                slide.shapes.title.text = slide_data.title

            # Add content to body placeholder if available
            if len(slide.placeholders) > 1:
                body = slide.placeholders[1]
                body.text = slide_data.text

        return prs

    @staticmethod
    def read_ppt_file(file_path: str) -> List[dict]:
        """Read an existing PowerPoint file and extract slide content"""
        try:
            prs = Presentation(file_path)
            slides_content = []

            for i, slide in enumerate(prs.slides):
                slide_data = {
                    "slide_number": i + 1,
                    "title": "",
                    "content": [],
                    "layout_name": slide.slide_layout.name if hasattr(slide.slide_layout, 'name') else f"Layout {i}"
                }

                # Extract text from all shapes
                for shape in slide.shapes:
                    if shape.has_text_frame:
                        text = shape.text.strip()
                        if text:
                            # Try to identify if it's a title
                            if shape == slide.shapes.title:
                                slide_data["title"] = text
                            else:
                                slide_data["content"].append(text)

                slides_content.append(slide_data)

            return slides_content
        except Exception as e:
            console.print(f"[red]Error reading PowerPoint file: {e}[/red]")
            return []

    @staticmethod
    def print_existing_ppt(file_path: str):
        """Print contents of an existing PowerPoint file to terminal"""
        slides_content = PPTSlideDisplay.read_ppt_file(file_path)

        if not slides_content:
            console.print(f"[red]Could not read slides from {file_path}[/red]")
            return

        file_name = Path(file_path).name
        console.print(f"\n[bold blue]📖 Reading: {file_name}[/bold blue]")
        console.print("=" * 60)

        for slide_data in slides_content:
            # Create slide content display
            slide_content = Text()
            slide_content.append(f"Layout: {slide_data['layout_name']}\n", style="dim")

            if slide_data['content']:
                for content_item in slide_data['content']:
                    slide_content.append(f"{content_item}\n", style="white")
            else:
                slide_content.append("(No content)", style="dim italic")

            title = slide_data['title'] if slide_data['title'] else f"Slide {slide_data['slide_number']}"

            panel = Panel(
                slide_content,
                title=f"[bold]Slide {slide_data['slide_number']}: {title}[/bold]",
                border_style="green",
                padding=(1, 2)
            )
            console.print(panel)
            console.print()  # Add spacing between slides

    @staticmethod
    def print_slides_to_terminal(slides: List[Slide], title: str = "Presentation"):
        """Print slides in a formatted way to terminal"""
        console.print(f"\n[bold blue]📊 {title}[/bold blue]")
        console.print("=" * 60)

        for i, slide in enumerate(slides, 1):
            # Create a panel for each slide
            slide_content = Text()
            slide_content.append(f"Layout: {slide.layout}\n", style="dim")
            slide_content.append(slide.text, style="white")

            panel = Panel(
                slide_content,
                title=f"[bold]Slide {i}: {slide.title}[/bold]",
                border_style="blue",
                padding=(1, 2)
            )
            console.print(panel)
            console.print()  # Add spacing between slides

    @staticmethod
    def save_ppt_to_tmp(slides: List[Slide], filename: str = "generated_slides.pptx") -> str:
        """Save PowerPoint to .tmp directory and return the path"""
        prs = PPTSlideDisplay.create_ppt_from_slides(slides, "Generated Presentation")

        # Ensure filename has .pptx extension
        if not filename.endswith('.pptx'):
            filename += '.pptx'

        # Add .tmp_ prefix to filename
        tmp_filename = f".tmp_{filename}"
        file_path = TMP_DIR / tmp_filename

        prs.save(str(file_path))
        return str(file_path)

    @staticmethod
    def print_file_links(file_paths: List[str]):
        """Print clickable file links"""
        console.print("\n[bold green]📁 Generated Files:[/bold green]")

        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("File", style="cyan")
        table.add_column("Path", style="green")
        table.add_column("Size", style="yellow")

        for file_path in file_paths:
            path_obj = Path(file_path)
            if path_obj.exists():
                size = f"{path_obj.stat().st_size / 1024:.1f} KB"
                table.add_row(path_obj.name, str(path_obj.absolute()), size)

        console.print(table)

#----- MAIN EXECUTION -----

# Option 2: Multi-line string with YAML content
yaml_content = """
slides:
  - title: first title
    text: some text
    layout: 0
  - title: second title
    text: more text
    layout: 1
  - title: third slide
    text: with the previous layout
    layout: 1
"""

slides_data = yaml.safe_load(yaml_content)

# Convert to Slide objects
slides: List[Slide] = [
    Slide(**slide_dict) for slide_dict in slides_data['slides']
]

# Print original data structure
console.print("\n[bold yellow]📋 Original YAML Data:[/bold yellow]")
rprint(slides_data)

# Print slides to terminal in formatted way
PPTSlideDisplay.print_slides_to_terminal(slides, "Sample Presentation")

# Save to .tmp directory
saved_file = PPTSlideDisplay.save_ppt_to_tmp(slides, "sample_presentation")

# Print file links
PPTSlideDisplay.print_file_links([saved_file])

# # Option 3: Python data structures directly (now working)
console.print("\n[bold cyan]🔧 Alternative: Direct Python Objects[/bold cyan]")
direct_slides: List[Slide] = [
    Slide(title="Direct Slide 1", text="Created directly from Python", layout=0),
    Slide(title="Direct Slide 2", text="No YAML needed", layout=1),
    Slide(title="Direct Slide 3", text="Pure Python approach", layout=1)
]

PPTSlideDisplay.print_slides_to_terminal(direct_slides, "Direct Python Slides")
direct_saved_file = PPTSlideDisplay.save_ppt_to_tmp(direct_slides, "direct_presentation")
PPTSlideDisplay.print_file_links([direct_saved_file])

# Demonstrate reading existing PowerPoint files
console.print("\n[bold magenta]📚 Reading Existing PowerPoint Files[/bold magenta]")

# Check for existing PowerPoint files in .tmp directory
tmp_ppt_files = list(TMP_DIR.glob("*.pptx"))
if tmp_ppt_files:
    # Read the first PowerPoint file found
    existing_file = tmp_ppt_files[0]
    console.print(f"\n[dim]Found existing file: {existing_file}[/dim]")
    PPTSlideDisplay.print_existing_ppt(str(existing_file))
else:
    console.print("\n[dim]No existing PowerPoint files found in .tmp directory[/dim]")

# Summary
console.print("\n[bold green]✅ Summary[/bold green]")
console.print("=" * 40)
console.print("• Fixed print functionality ✓")
console.print("• Can print PPT slides to terminal ✓")
console.print("• Saves files to .tmp directory ✓")
console.print("• Prints clickable file links ✓")
console.print("• Can read existing PowerPoint files ✓")
console.print(f"• Generated {len([saved_file, direct_saved_file])} new PowerPoint files")


