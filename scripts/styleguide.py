from dataclasses import dataclass
from typing import Dict, <PERSON><PERSON>

@dataclass
class StyleGuide:
    """Style guide configuration for private equity presentation slides."""
    
    # Primary brand colors (RGB tuples)
    primary_blue: Tuple[int, int, int] = (0, 51, 102)  # Apollo dark blue
    secondary_blue: <PERSON><PERSON>[int, int, int] = (51, 102, 153)  # Lighter blue for accents
    accent_gold: Tuple[int, int, int] = (204, 153, 0)  # Gold for highlights
    
    # Text colors
    text_primary: Tuple[int, int, int] = (33, 33, 33)  # Dark gray for body text
    text_secondary: Tuple[int, int, int] = (102, 102, 102)  # Medium gray for secondary text
    text_white: Tuple[int, int, int] = (255, 255, 255)  # White for dark backgrounds
    
    # Background colors
    bg_white: Tuple[int, int, int] = (255, 255, 255)  # Primary background
    bg_light_gray: Tuple[int, int, int] = (248, 248, 248)  # Subtle background
    bg_dark: <PERSON><PERSON>[int, int, int] = (0, 51, 102)  # Dark background for title slides
    
    # Chart colors (for financial data visualization)
    chart_positive: Tuple[int, int, int] = (0, 128, 0)  # Green for positive performance
    chart_negative: Tuple[int, int, int] = (204, 0, 0)  # Red for negative performance
    chart_neutral: Tuple[int, int, int] = (102, 102, 102)  # Gray for neutral data
    chart_series: list = None  # Will be set in __post_init__
    
    # Font specifications
    font_title: str = "Calibri"  # Title font
    font_body: str = "Calibri"  # Body text font
    font_size_title: int = 44  # Title slide font size
    font_size_subtitle: int = 24  # Subtitle font size
    font_size_body: int = 18  # Body text font size
    font_size_caption: int = 12  # Caption/footnote font size
    
    def __post_init__(self):
        """Initialize chart series colors."""
        self.chart_series = [
            self.primary_blue,
            self.accent_gold,
            self.secondary_blue,
            (153, 76, 0),  # Brown for additional series
            (76, 153, 0),  # Green for additional series
        ]
    
    def get_rgb_dict(self, color: Tuple[int, int, int]) -> Dict[str, int]:
        """Convert RGB tuple to dictionary for pptx RGBColor."""
        return {"r": color[0], "g": color[1], "b": color[2]}
