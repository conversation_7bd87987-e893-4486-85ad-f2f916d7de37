# pypptx_q.py

from pydantic import BaseModel
from typing import List
import yaml
from pathlib import Path
from rich import print as rprint
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from pptx import Presentation

# Ensure .tmp directory exists
TMP_DIR = Path(".tmp")
TMP_DIR.mkdir(exist_ok=True)

console = Console()

class Slide(BaseModel):
    title: str
    text: str
    layout: int

class PPTSlideDisplay:
    """Helper class for displaying and saving PPT slides"""

    @staticmethod
    def create_ppt_from_slides(slides: List[Slide], title: str = "Generated Presentation") -> Presentation:
        """Create a PowerPoint presentation from slide data"""
        prs = Presentation()

        # Create title slide
        title_slide_layout = prs.slide_layouts[0]
        title_slide = prs.slides.add_slide(title_slide_layout)
        title_slide.shapes.title.text = title

        # Create content slides
        for slide_data in slides:
            slide_layout = prs.slide_layouts[slide_data.layout]
            slide = prs.slides.add_slide(slide_layout)

            if slide.shapes.title:
                slide.shapes.title.text = slide_data.title

            # Add content to body placeholder if available
            if len(slide.placeholders) > 1:
                body = slide.placeholders[1]
                body.text = slide_data.text

        return prs

    @staticmethod
    def print_slides_to_terminal(slides: List[Slide], title: str = "Presentation"):
        """Print slides in a formatted way to terminal"""
        console.print(f"\n[bold blue]📊 {title}[/bold blue]")
        console.print("=" * 60)

        for i, slide in enumerate(slides, 1):
            # Create a panel for each slide
            slide_content = Text()
            slide_content.append(f"Layout: {slide.layout}\n", style="dim")
            slide_content.append(slide.text, style="white")

            panel = Panel(
                slide_content,
                title=f"[bold]Slide {i}: {slide.title}[/bold]",
                border_style="blue",
                padding=(1, 2)
            )
            console.print(panel)
            console.print()  # Add spacing between slides

    @staticmethod
    def save_ppt_to_tmp(slides: List[Slide], filename: str = "generated_slides.pptx") -> str:
        """Save PowerPoint to .tmp directory and return the path"""
        prs = PPTSlideDisplay.create_ppt_from_slides(slides, "Generated Presentation")

        # Ensure filename has .pptx extension
        if not filename.endswith('.pptx'):
            filename += '.pptx'

        # Add .tmp_ prefix to filename
        tmp_filename = f".tmp_{filename}"
        file_path = TMP_DIR / tmp_filename

        prs.save(str(file_path))
        return str(file_path)

    @staticmethod
    def print_file_links(file_paths: List[str]):
        """Print clickable file links"""
        console.print("\n[bold green]📁 Generated Files:[/bold green]")

        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("File", style="cyan")
        table.add_column("Path", style="green")
        table.add_column("Size", style="yellow")

        for file_path in file_paths:
            path_obj = Path(file_path)
            if path_obj.exists():
                size = f"{path_obj.stat().st_size / 1024:.1f} KB"
                table.add_row(path_obj.name, str(path_obj.absolute()), size)

        console.print(table)

#----- MAIN EXECUTION -----

# Option 2: Multi-line string with YAML content
yaml_content = """
slides:
  - title: first title
    text: some text
    layout: 0
  - title: second title
    text: more text
    layout: 1
  - title: third slide
    text: with the previous layout
    layout: 1
"""

slides_data = yaml.safe_load(yaml_content)

# Convert to Slide objects
slides: List[Slide] = [
    Slide(**slide_dict) for slide_dict in slides_data['slides']
]

# Print original data structure
console.print("\n[bold yellow]📋 Original YAML Data:[/bold yellow]")
rprint(slides_data)

# Print slides to terminal in formatted way
PPTSlideDisplay.print_slides_to_terminal(slides, "Sample Presentation")

# Save to .tmp directory
saved_file = PPTSlideDisplay.save_ppt_to_tmp(slides, "sample_presentation")

# Print file links
PPTSlideDisplay.print_file_links([saved_file])

# # Option 3: Python data structures directly (now working)
console.print("\n[bold cyan]🔧 Alternative: Direct Python Objects[/bold cyan]")
direct_slides: List[Slide] = [
    Slide(title="Direct Slide 1", text="Created directly from Python", layout=0),
    Slide(title="Direct Slide 2", text="No YAML needed", layout=1),
    Slide(title="Direct Slide 3", text="Pure Python approach", layout=1)
]

PPTSlideDisplay.print_slides_to_terminal(direct_slides, "Direct Python Slides")
direct_saved_file = PPTSlideDisplay.save_ppt_to_tmp(direct_slides, "direct_presentation")
PPTSlideDisplay.print_file_links([direct_saved_file])


