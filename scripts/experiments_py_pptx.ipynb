import pptx
from pptx import Presentation
from rich import print as rprint

from rich.table import Table
from rich.console import Console

console = Console()

pptx_example_file = "/Users/<USER>/Documents/Code3/to_ppt/.tmp/Jazz Pharma 6 slides copy.pptx"

prs = Presentation(pptx_example_file)

# display each slide in notebook format.
# for i, slide in enumerate(prs.slides):
#     print(f"\n--- SLIDE {i+1} ---")
#     for shape in slide.shapes:
#         if shape.has_text_frame:
#             for paragraph in shape.text_frame.paragraphs:
#                 text = paragraph.text.strip()
#                 if text:
#                     print(f"  {text}")  # don't use rprint here or gaps.

#--------------------------------
# Display slides in a 3x2 rich table format
#--------------------------------


# Create table with 3 columns and 2 rows
table = Table(title="Slide Content Overview", show_header=True, header_style="")
table.add_column("Slide 1&4", style="cyan", width=100)
table.add_column("Slide 2&5", style="green", width=100)
table.add_column("Slide 3&6", style="yellow", width=100)

# Collect slide texts
slide_texts = []
for i, slide in enumerate(prs.slides):
    slide_text = []
    for shape in slide.shapes:
        if shape.has_text_frame:
            for paragraph in shape.text_frame.paragraphs:
                text = paragraph.text.strip()
                if text:
                    slide_text.append(text)
    slide_texts.append("\n".join(slide_text) if slide_text else f"[No text in slide {i+1}]")

# Add first row
row_data = []
for col in range(3):
    slide_idx = col
    if slide_idx < len(slide_texts):
        # Truncate long text for table display
        text = slide_texts[slide_idx]
        if len(text) > 200:
            text = text[:197] + "..."
        row_data.append(text)
    else:
        row_data.append("[Empty]")
table.add_row(*row_data)

# Add divider
table.add_section()

# Add second row
row_data = []
for col in range(3):
    slide_idx = 3 + col
    if slide_idx < len(slide_texts):
        # Truncate long text for table display
        text = slide_texts[slide_idx]
        if len(text) > 200:
            text = text[:197] + "..."
        row_data.append(text)
    else:
        row_data.append("[Empty]")
table.add_row(*row_data)

console.print(table)


# Extract all text from slides in presentation
from pptx import Presentation
from rich import print as rprint

prs = Presentation(pptx_example_file)

# text_runs will be populated with a list of strings,
# one for each text run in presentation
text_runs = []

for slide in prs.slides:
    for shape in slide.shapes:
        if not shape.has_text_frame:
            continue
        for paragraph in shape.text_frame.paragraphs:
            for run in paragraph.runs:
                text_runs.append(run.text)
rprint(text_runs)

# add_table() example
# ../_images/add-table.png

from pptx import Presentation  # import main presentation class
from pptx.util import Inches  # import unit helper for measurements

# prs = Presentation()  # commented out - using existing prs object
title_only_slide_layout = prs.slide_layouts[5]  # get layout with title placeholder only
slide = prs.slides.add_slide(title_only_slide_layout)  # add new slide to presentation
shapes = slide.shapes  # get shapes collection for this slide

shapes.title.text = 'Adding a Table'  # set slide title text

rows = cols = 2  # define 2x2 table dimensions
left = top = Inches(2.0)  # position table 2 inches from left and top
width = Inches(6.0)  # table width of 6 inches
height = Inches(0.8)  # table height of 0.8 inches

table = shapes.add_table(rows, cols, left, top, width, height).table  # create table and get table object

# set column widths
table.columns[0].width = Inches(2.0)  # first column 2 inches wide
table.columns[1].width = Inches(4.0)  # second column 4 inches wide

# write column headings
table.cell(0, 0).text = 'Foo'  # row 0, col 0 - top left cell
table.cell(0, 1).text = 'Bar'  # row 0, col 1 - top right cell

# write body cells
table.cell(1, 0).text = 'Baz'  # row 1, col 0 - bottom left cell
table.cell(1, 1).text = 'Qux'  # row 1, col 1 - bottom right cell

prs.save('test.pptx')  # save presentation to file